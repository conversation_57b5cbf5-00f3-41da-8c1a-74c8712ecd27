// JetTheme v2.9 - Advanced Service Worker
// Ultra-optimized caching strategy for maximum performance

const CACHE_NAME = 'jettheme-v2.9';
const STATIC_CACHE = 'jettheme-static-v2.9';
const DYNAMIC_CACHE = 'jettheme-dynamic-v2.9';
const IMAGE_CACHE = 'jettheme-images-v2.9';

// Critical resources to cache immediately
const CRITICAL_RESOURCES = [
    '/',
    '/css/critical.css',
    '/js/critical.js',
    '/images/logo.png',
    '/images/favicon.png',
    'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css'
];

// Resources to cache on demand
const CACHE_PATTERNS = {
    static: [
        /\.(?:js|css|woff2?|ttf|eot)$/,
        /\/static\//,
        /\/assets\//
    ],
    images: [
        /\.(?:png|jpg|jpeg|svg|gif|webp|avif)$/,
        /\/images\//,
        /\/uploads\//
    ],
    pages: [
        /\/$/,
        /\/p\//,
        /\/search\//
    ]
};

// Cache strategies
const STRATEGIES = {
    CACHE_FIRST: 'cache-first',
    NETWORK_FIRST: 'network-first',
    STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
    NETWORK_ONLY: 'network-only',
    CACHE_ONLY: 'cache-only'
};

// Install event - cache critical resources
self.addEventListener('install', event => {
    console.log('SW: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('SW: Caching critical resources');
                return cache.addAll(CRITICAL_RESOURCES);
            })
            .then(() => {
                console.log('SW: Critical resources cached');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('SW: Failed to cache critical resources', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('SW: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && 
                            cacheName !== DYNAMIC_CACHE && 
                            cacheName !== IMAGE_CACHE) {
                            console.log('SW: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('SW: Activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') return;
    
    // Skip external requests (except fonts and CDN)
    if (url.origin !== location.origin && 
        !url.hostname.includes('fonts.googleapis.com') &&
        !url.hostname.includes('fonts.gstatic.com') &&
        !url.hostname.includes('cdn.jsdelivr.net')) {
        return;
    }
    
    event.respondWith(handleRequest(request));
});

// Main request handler
async function handleRequest(request) {
    const url = new URL(request.url);
    const pathname = url.pathname;
    
    try {
        // Determine cache strategy based on request type
        if (isStaticResource(pathname)) {
            return await cacheFirst(request, STATIC_CACHE);
        } else if (isImageResource(pathname)) {
            return await staleWhileRevalidate(request, IMAGE_CACHE);
        } else if (isPageResource(pathname)) {
            return await networkFirst(request, DYNAMIC_CACHE);
        } else {
            return await staleWhileRevalidate(request, DYNAMIC_CACHE);
        }
    } catch (error) {
        console.error('SW: Request failed', error);
        return await handleOffline(request);
    }
}

// Cache-first strategy (for static resources)
async function cacheFirst(request, cacheName) {
    const cache = await caches.open(cacheName);
    const cached = await cache.match(request);
    
    if (cached) {
        // Update cache in background
        fetch(request).then(response => {
            if (response.ok) {
                cache.put(request, response.clone());
            }
        }).catch(() => {});
        
        return cached;
    }
    
    const response = await fetch(request);
    if (response.ok) {
        cache.put(request, response.clone());
    }
    return response;
}

// Network-first strategy (for pages)
async function networkFirst(request, cacheName) {
    const cache = await caches.open(cacheName);
    
    try {
        const response = await fetch(request);
        if (response.ok) {
            cache.put(request, response.clone());
        }
        return response;
    } catch (error) {
        const cached = await cache.match(request);
        if (cached) {
            return cached;
        }
        throw error;
    }
}

// Stale-while-revalidate strategy (for images and dynamic content)
async function staleWhileRevalidate(request, cacheName) {
    const cache = await caches.open(cacheName);
    const cached = await cache.match(request);
    
    const fetchPromise = fetch(request).then(response => {
        if (response.ok) {
            cache.put(request, response.clone());
        }
        return response;
    }).catch(() => {});
    
    return cached || fetchPromise;
}

// Handle offline scenarios
async function handleOffline(request) {
    const url = new URL(request.url);
    
    // Return cached page or offline page
    if (request.destination === 'document') {
        const cache = await caches.open(DYNAMIC_CACHE);
        const cached = await cache.match('/') || await cache.match('/offline.html');
        return cached || new Response('Offline', { status: 503 });
    }
    
    // Return placeholder for images
    if (request.destination === 'image') {
        return new Response(
            '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="150" viewBox="0 0 200 150"><rect width="200" height="150" fill="#f0f0f0"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#999">Image unavailable</text></svg>',
            { headers: { 'Content-Type': 'image/svg+xml' } }
        );
    }
    
    return new Response('Resource unavailable', { status: 503 });
}

// Helper functions
function isStaticResource(pathname) {
    return CACHE_PATTERNS.static.some(pattern => pattern.test(pathname));
}

function isImageResource(pathname) {
    return CACHE_PATTERNS.images.some(pattern => pattern.test(pathname));
}

function isPageResource(pathname) {
    return CACHE_PATTERNS.pages.some(pattern => pattern.test(pathname));
}

// Background sync for failed requests
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    // Implement background sync logic here
    console.log('SW: Background sync triggered');
}

// Push notifications
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: '/images/icon-192.png',
            badge: '/images/badge-72.png',
            vibrate: [100, 50, 100],
            data: data.data || {}
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Notification click handler
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    event.waitUntil(
        clients.openWindow(event.notification.data.url || '/')
    );
});

console.log('SW: Service Worker loaded successfully');
